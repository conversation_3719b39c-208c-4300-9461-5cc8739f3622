<?php
header('Content-Type: application/json');

// Optional: Only allow requests from the launcher
if (!isset($_SERVER['HTTP_X_LAUNCHER_REQUEST']) || $_SERVER['HTTP_X_LAUNCHER_REQUEST'] !== 'true') {
    http_response_code(403);
    echo json_encode(['error' => 'Forbidden']);
    exit;
}

// 1. Connect to your database
$serverName = "localhost,1433";
$connectionOptions = [
    "Database" => "aion4.8-news",
    "Uid" => "sa",
    "PWD" => "nMHcCAQEEIDojD@+"
];
$conn = sqlsrv_connect($serverName, $connectionOptions);
if ($conn === false) {
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed']);
    exit;
}

// 2. Query the news table
$sql = "SELECT TOP 10 * FROM website_news WHERE is_published = 1 ORDER BY created_at DESC";
$stmt = sqlsrv_query($conn, $sql);
$news = [];
if ($stmt !== false) {
    while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
        $news[] = [
            "id" => (int)$row['id'],
            "title" => $row['title'],
            "subtitle" => $row['subtitle'],
            "content" => $row['content'],
            "image_url" => $row['image_url'],
            "category" => $row['category'],
            "created_at" => isset($row['created_at']) ? (is_object($row['created_at']) ? $row['created_at']->format('c') : $row['created_at']) : null,
            "updated_at" => isset($row['updated_at']) ? (is_object($row['updated_at']) ? $row['updated_at']->format('c') : $row['updated_at']) : null
        ];
    }
}

// 3. Output as JSON
echo json_encode(["news" => $news]);
?>