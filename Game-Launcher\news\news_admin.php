<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Let <PERSON><PERSON><PERSON><PERSON> know it's a launcher request (bypasses security header issues)
if (!headers_sent()) {
    header("X-Launcher-Request: true");
}


// --- CONFIG --- //
$serverName = "localhost,1433";
$connectionOptions = [
    "Database" => "aion4.8-news",
    "Uid" => "sa",
    "PWD" => "nMHcCAQEEIDojD@+"
];
$conn = sqlsrv_connect($serverName, $connectionOptions);
if ($conn === false) {
    die("DB connection failed: " . print_r(sqlsrv_errors(), true));
}

session_start();
$admin_password = "nMHcCAQEEIDojD@+"; // CHANGE THIS!

if (isset($_POST['logout'])) { session_destroy(); header("Location: ?"); exit; }
if (!isset($_SESSION['admin'])) {
    if (isset($_POST['password']) && $_POST['password'] === $admin_password) {
        $_SESSION['admin'] = true;
    } else {
        echo '<form method="post" style="margin:100px auto;max-width:350px;background:#232b3b;padding:30px;border-radius:10px;box-shadow:0 2px 12px #0003;">
        <h2 style="text-align:center;color:#27c9ae;">Admin Login</h2>
        <input type="password" name="password" placeholder="Admin password" style="width:100%;padding:10px;margin:10px 0;border-radius:5px;border:1px solid #444;background:#222;color:#eee;">
        <button type="submit" style="width:100%;padding:10px;background:#27c9ae;color:#111;border:none;border-radius:5px;font-size:1.1em;">Login</button>
        </form>';
        exit;
    }
}

// --- Handle Add/Edit/Delete --- //
function handleImageUpload($inputName, $existing = '') {
    if (isset($_FILES[$inputName]) && $_FILES[$inputName]['error'] === UPLOAD_ERR_OK) {
        $ext = strtolower(pathinfo($_FILES[$inputName]['name'], PATHINFO_EXTENSION));
        $allowed = ['jpg','jpeg','png','gif','webp'];
        if (!in_array($ext, $allowed)) return $existing;
        $target = "images/news_" . time() . "_" . rand(1000,9999) . "." . $ext;
        if (move_uploaded_file($_FILES[$inputName]['tmp_name'], __DIR__ . "/$target")) {
            return "https://www.neoaion.fr/Game-Launcher/news/$target";
        }
    }
    return $existing;
}

// --- TAB NAVIGATION --- //
$activeTab = isset($_GET['tab']) && $_GET['tab'] === 'patching' ? 'patching' : 'news';

if ($activeTab === 'patching') {
    $patchFile = "C:/xampp/htdocs/Game-Launcher/Patches/patches.json";
    $patches = [];
    if (file_exists($patchFile)) {
        $patches = json_decode(file_get_contents($patchFile), true) ?: [];
    }
    // Handle Add/Edit/Delete
    if (isset($_POST['add_patch'])) {
        $patches[] = [
            "id" => $_POST['id'],
            "filename" => $_POST['filename'],
            "version" => $_POST['version'],
            "description" => $_POST['description'],
            "hash" => $_POST['hash']
        ];
        file_put_contents($patchFile, json_encode($patches, JSON_PRETTY_PRINT));
        header("Location: ?tab=patching"); exit;
    }
    if (isset($_POST['delete_patch'])) {
        array_splice($patches, $_POST['index'], 1);
        file_put_contents($patchFile, json_encode($patches, JSON_PRETTY_PRINT));
        header("Location: ?tab=patching"); exit;
    }
    if (isset($_POST['generate_hash']) && isset($_FILES['patchfile'])) {
        $filePath = $_FILES['patchfile']['tmp_name'];
        // Execute PowerShell script to generate hash with proper quoting
        $psScript = "Get-FileHash -Path \"$filePath\" -Algorithm SHA256 | ForEach-Object { \$bytes = for (\$i=0; \$i -lt \$_.Hash.Length; \$i+=2) { [Convert]::ToByte(\$_.Hash.Substring(\$i,2),16) }; [Convert]::ToBase64String(\$bytes) }";
        $hash = shell_exec("powershell -Command \"$psScript\"");
        $hash = trim($hash); // Remove any whitespace
    }
} else {
    // News handling
    if (isset($_POST['add'])) {
        $img = handleImageUpload('image', $_POST['image_url']);
        $sql = "INSERT INTO website_news (title, subtitle, content, image_url, category, is_published) VALUES (?, ?, ?, ?, ?, 1)";
        $params = [$_POST['title'], $_POST['subtitle'], $_POST['content'], $img, $_POST['category']];
        sqlsrv_query($conn, $sql, $params);
    }
    if (isset($_POST['edit'])) {
        $img = handleImageUpload('image', $_POST['image_url']);
        $sql = "UPDATE website_news SET title=?, subtitle=?, content=?, image_url=?, category=? WHERE id=?";
        $params = [$_POST['title'], $_POST['subtitle'], $_POST['content'], $img, $_POST['category'], $_POST['id']];
        sqlsrv_query($conn, $sql, $params);
    }
    if (isset($_POST['delete'])) {
        $sql = "DELETE FROM website_news WHERE id=?";
        $params = [$_POST['id']];
        sqlsrv_query($conn, $sql, $params);
    }

    $sql = "SELECT * FROM website_news ORDER BY created_at DESC";
    $stmt = sqlsrv_query($conn, $sql);
    $news = [];
    if ($stmt !== false) {
        while ($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC)) {
            $news[] = $row;
        }
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Admin Panel</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <style>
        body { font-family: 'Segoe UI', Arial, sans-serif; margin: 0; background: #181c24; color: #eee; }
        .container { max-width: 900px; margin: 40px auto; background: #232b3b; border-radius: 12px; box-shadow: 0 2px 16px #0005; padding: 30px; }
        h1 { color: #27c9ae; text-align: center; }
        .logout { float: right; margin-top: -10px; }
        .tabs { display: flex; gap: 10px; margin-bottom: 20px; background: #1a1f2b; padding: 10px; border-radius: 8px; }
        .tabs a { padding: 10px 20px; color: #eee; text-decoration: none; border-radius: 5px; transition: all 0.3s; }
        .tabs a:hover { background: #2a3444; }
        .tabs a.active { background: #27c9ae; color: #111; }
        .add-form, .edit-form, .patch-form { background: #222; padding: 20px; border-radius: 10px; margin-bottom: 30px; }
        .add-form h2, .edit-form h2, .patch-form h2 { margin-top: 0; color: #27c9ae; }
        input, textarea, select { width: 100%; background: #232b3b; color: #eee; border: 1px solid #444; padding: 8px; border-radius: 5px; margin-bottom: 10px; }
        button { background: #27c9ae; color: #111; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer; font-weight: 600; }
        button.delete { background: #FF4655; color: #fff; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 30px; background: #232b3b; border-radius: 10px; overflow: hidden; }
        th, td { border: 1px solid #333; padding: 10px; text-align: left; }
        th { background: #222; color: #27c9ae; }
        .img-preview { max-width: 120px; max-height: 60px; border-radius: 6px; border: 1px solid #444; }
        @media (max-width: 700px) {
            .container { padding: 10px; }
            table, th, td { font-size: 0.95em; }
        }
    </style>
    <script>
    function previewImage(input, previewId) {
        // No-op: preview logic removed
    }
    function previewImageUrl(url, previewId, fileInputId = null) {
        // No-op: preview logic removed
    }
    document.addEventListener('DOMContentLoaded', function() {
        // No preview logic on load
    });
    </script>
</head>
<body>
    <div class="container">
        <form method="post" class="logout"><button name="logout">Logout</button></form>
        <h1>Admin Panel</h1>
        
        <!-- Tab Navigation -->
        <div class="tabs">
            <a href="?tab=news" class="<?= $activeTab=='news'?'active':'' ?>">News</a>
            <a href="?tab=patching" class="<?= $activeTab=='patching'?'active':'' ?>">Patching</a>
        </div>

        <?php if ($activeTab === 'patching'): ?>
            <h2>Patching Admin</h2>
            <?php
            // Show error or success message
            if (isset($_POST['generate_hash'])) {
                if (empty($hash)) {
                    echo '<div style="color:#FF4655;margin-bottom:10px;">Failed to generate hash. Make sure you selected a file and your server supports PowerShell.</div>';
                } else {
                    echo '<div style="color:#27c9ae;margin-bottom:10px;">Hash generated successfully!</div>';
                }
            }
            ?>
            <form method="post" enctype="multipart/form-data" class="patch-form">
                <h3>Add Patch</h3>
                <input name="id" placeholder="Patch ID" required value="<?= isset($_POST['id']) ? htmlspecialchars($_POST['id']) : '' ?>">
                <input name="filename" placeholder="Filename" required value="<?= isset($_POST['filename']) ? htmlspecialchars($_POST['filename']) : '' ?>">
                <input name="version" placeholder="Version" required value="<?= isset($_POST['version']) ? htmlspecialchars($_POST['version']) : '' ?>">
                <input name="description" placeholder="Description" required value="<?= isset($_POST['description']) ? htmlspecialchars($_POST['description']) : '' ?>">
                <input name="hash" placeholder="Hash" value="<?= isset($hash) ? htmlspecialchars($hash) : (isset($_POST['hash']) ? htmlspecialchars($_POST['hash']) : '') ?>" <?= isset($_POST['add_patch']) ? 'required' : '' ?>>
                <input type="file" name="patchfile">
                <button name="generate_hash" value="1" type="submit">Generate Hash</button>
                <button name="add_patch" value="1" type="submit">Add Patch</button>
            </form>
            <table>
                <tr>
                    <th>ID</th><th>Filename</th><th>Version</th><th>Description</th><th>Hash</th><th>Actions</th>
                </tr>
                <?php foreach ($patches as $i => $p): ?>
                <tr>
                    <td><?= htmlspecialchars($p['id']) ?></td>
                    <td><?= htmlspecialchars($p['filename']) ?></td>
                    <td><?= htmlspecialchars($p['version']) ?></td>
                    <td><?= htmlspecialchars($p['description']) ?></td>
                    <td style="font-size:0.9em;word-break:break-all;"><?= htmlspecialchars($p['hash']) ?></td>
                    <td>
                        <form method="post" style="display:inline;">
                            <input type="hidden" name="index" value="<?= $i ?>">
                            <button name="delete_patch" value="1" onclick="return confirm('Delete this patch?')" class="delete">Delete</button>
                        </form>
                    </td>
                </tr>
                <?php endforeach; ?>
            </table>
        <?php else: ?>
            <form method="post" class="add-form" enctype="multipart/form-data">
                <h2>Add News</h2>
                <input name="title" placeholder="Title" required>
                <input name="subtitle" placeholder="Subtitle">
                <textarea name="content" placeholder="Content (HTML allowed)" required></textarea>
                <input name="image_url" id="addImageUrl" placeholder="Image URL (or upload below)">
                <select name="category">
                    <option value="info">Info</option>
                    <option value="maj">Update</option>
                    <option value="event">Event</option>
                </select>
                <button name="add">Add News</button>
            </form>
            <h2>All News</h2>
            <table>
                <tr>
                    <th>ID</th><th>Title</th><th>Subtitle</th><th>Category</th><th>Image</th><th>Created</th><th>Actions</th>
                </tr>
                <?php foreach ($news as $n): ?>
                <form method="post" enctype="multipart/form-data" class="edit-form">
                <tr>
                    <td><?= $n['id'] ?><input type="hidden" name="id" value="<?= $n['id'] ?>"></td>
                    <td><input name="title" value="<?= htmlspecialchars($n['title']) ?>"></td>
                    <td><input name="subtitle" value="<?= htmlspecialchars($n['subtitle']) ?>"></td>
                    <td>
                        <select name="category">
                            <option value="info" <?= $n['category']=='info'?'selected':'' ?>>Info</option>
                            <option value="maj" <?= $n['category']=='maj'?'selected':'' ?>>Update</option>
                            <option value="event" <?= $n['category']=='event'?'selected':'' ?>>Event</option>
                        </select>
                    </td>
                    <td>
                        <input name="image_url" id="editImageUrl<?= $n['id'] ?>" value="<?= htmlspecialchars($n['image_url']) ?>" placeholder="Image URL (or upload)">
                    </td>
                    <td><?= isset($n['created_at']) ? (is_object($n['created_at']) ? $n['created_at']->format('Y-m-d H:i:s') : $n['created_at']) : '' ?></td>
                    <td class="actions">
                        <button name="edit">Save</button>
                        <button name="delete" class="delete" onclick="return confirm('Delete this news?')">Delete</button>
                    </td>
                </tr>
                <tr>
                    <td colspan="7">
                        <textarea name="content" rows="3"><?= htmlspecialchars($n['content']) ?></textarea>
                    </td>
                </tr>
                </form>
                <?php endforeach; ?>
            </table>
        <?php endif; ?>
    </div>
</body>
</html>