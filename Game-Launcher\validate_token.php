<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Simple file-based token storage (you can upgrade to database later)
$token_file = 'valid_tokens.json';

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['action'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid request']);
    exit;
}

$action = $input['action'];
$token = isset($input['token']) ? $input['token'] : '';
$ip = isset($input['ip']) ? $input['ip'] : '';
$client_ip = isset($input['client_ip']) ? $input['client_ip'] : '';

// Load existing tokens
$valid_tokens = [];
if (file_exists($token_file)) {
    $valid_tokens = json_decode(file_get_contents($token_file), true) ?: [];
}

if ($action === 'register') {
    // Register a new token
    if (empty($token)) {
        echo json_encode(['success' => false, 'error' => 'Missing token']);
        exit;
    }

    $parts = explode(':', $token);
    if (count($parts) !== 3) {
        echo json_encode(['success' => false, 'error' => 'Invalid token format']);
        exit;
    }
    
    $token_value = $parts[0];
    $timestamp = intval($parts[1]);
    $hash = $parts[2];
    
    // Verify token hash
    $expected_hash = hash('sha256', $token_value . $timestamp);
    if ($hash !== $expected_hash) {
        echo json_encode(['success' => false, 'error' => 'Invalid token hash']);
        exit;
    }
    
    // Check if token is not too old (5 minutes max)
    if (time() - $timestamp > 300) {
        echo json_encode(['success' => false, 'error' => 'Token expired']);
        exit;
    }
    
    // Store token with expiration (valid for 10 minutes)
    $valid_tokens[$token] = [
        'created' => time(),
        'expires' => time() + 600, // 10 minutes
        'used' => false,
        'ip' => !empty($client_ip) ? $client_ip : ($_SERVER['REMOTE_ADDR'] ?? 'unknown')
    ];
    
    // Clean up expired tokens
    foreach ($valid_tokens as $t => $data) {
        if ($data['expires'] < time()) {
            unset($valid_tokens[$t]);
        }
    }
    
    // Save tokens
    file_put_contents($token_file, json_encode($valid_tokens));
    
    echo json_encode(['success' => true, 'message' => 'Token registered']);
    
} elseif ($action === 'validate') {
    // Validate a token (called by game server)
    if (empty($token)) {
        echo json_encode(['success' => false, 'error' => 'Missing token']);
        exit;
    }

    if (!isset($valid_tokens[$token])) {
        echo json_encode(['success' => false, 'error' => 'Token not found']);
        exit;
    }
    
    $token_data = $valid_tokens[$token];
    
    // Check if token is expired
    if ($token_data['expires'] < time()) {
        unset($valid_tokens[$token]);
        file_put_contents($token_file, json_encode($valid_tokens));
        echo json_encode(['success' => false, 'error' => 'Token expired']);
        exit;
    }
    
    // Check if token was already used
    if ($token_data['used']) {
        echo json_encode(['success' => false, 'error' => 'Token already used']);
        exit;
    }
    
    // Mark token as used
    $valid_tokens[$token]['used'] = true;
    file_put_contents($token_file, json_encode($valid_tokens));
    
    echo json_encode(['success' => true, 'message' => 'Token valid']);

} elseif ($action === 'check_ip') {
    // Check if an IP has valid authentication
    if (empty($ip)) {
        echo json_encode(['success' => false, 'error' => 'Missing IP address']);
        exit;
    }

    // Look for any valid (unused) token from this IP
    $found_valid_token = false;
    $used_token_key = null;

    foreach ($valid_tokens as $token_key => $token_data) {
        if (isset($token_data['ip']) && $token_data['ip'] === $ip && !$token_data['used'] && $token_data['expires'] >= time()) {
            $found_valid_token = true;
            $used_token_key = $token_key;
            break;
        }
    }

    if ($found_valid_token && $used_token_key !== null) {
        // Mark the token as used to prevent reuse
        $valid_tokens[$used_token_key]['used'] = true;
        $valid_tokens[$used_token_key]['used_timestamp'] = time();

        // Save the updated tokens
        file_put_contents($token_file, json_encode($valid_tokens));

        echo json_encode(['success' => true, 'message' => 'IP has valid authentication']);
    } else {
        echo json_encode(['success' => false, 'error' => 'IP not authenticated']);
    }

} else {
    echo json_encode(['success' => false, 'error' => 'Invalid action']);
}
?>
